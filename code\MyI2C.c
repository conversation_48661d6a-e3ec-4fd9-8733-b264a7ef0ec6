#include "stdint.h"
#include "MyI2C.h"

#define SCL_PIN DL_GPIO_PIN_1 // SCL引脚
#define SCL_GPIO_PORT GPIOA // SCL引脚所在的GPIO端口
#define SDA_PIN DL_GPIO_PIN_0 // SDA引脚
#define SDA_GPIO_PORT GPIOA // SDA引脚所在的GPIO端口

/*引脚配置层*/

/**
 * 函    数：I2C写SCL引脚电平
 * 参    数：BitValue 协议层传入的当前需要写入SCL的电平，范围0~1
 * 返 回 值：无
 * 注意事项：此函数需要用户实现内容，当BitValue为0时，需要置SCL为低电平，当BitValue为1时，需要置SCL为高电平
 */
void MyI2C_W_SCL(uint8_t BitValue)
{
	if (BitValue)
		DL_GPIO_setPins(SCL_GPIO_PORT, SCL_PIN); // 置高
	else
		DL_GPIO_clearPins(SCL_GPIO_PORT, SCL_PIN); // 置低
}

/**
 * 函    数：I2C写SDA引脚电平
 * 参    数：BitValue 协议层传入的当前需要写入SDA的电平，范围0~1
 * 返 回 值：无
 * 注意事项：此函数需要用户实现内容，当BitValue为0时，需要置SDA为低电平，当BitValue为1时，需要置SDA为高电平
 */
void MyI2C_W_SDA(uint8_t BitValue)
{
	if (BitValue)
		DL_GPIO_setPins(SDA_GPIO_PORT, SDA_PIN); // 置高
	else
		DL_GPIO_clearPins(SDA_GPIO_PORT, SDA_PIN); // 置低
}

/**
 * 函    数：I2C读SDA引脚电平
 * 参    数：无
 * 返 回 值：协议层需要得到的当前SDA的电平，范围0~1
 * 注意事项：此函数需要用户实现内容，当前SDA为低电平时，返回0，当前SDA为高电平时，返回1
 */
uint8_t MyI2C_R_SDA(void)
{
	return DL_GPIO_readPins(SDA_GPIO_PORT, SDA_PIN) ? 1 : 0;
}

/**
 * 函    数：I2C初始化
 * 参    数：无
 * 返 回 值：无
 * 注意事项：此函数需要用户实现内容，实现SCL和SDA引脚的初始化
 */
void MyI2C_Init(void)
{

	/*设置默认电平*/
	MyI2C_W_SCL(1);
	MyI2C_W_SDA(1);
	// 设置PB10和PB11引脚初始化后默认为高电平（释放总线状态）
}

/*协议层*/

/**
 * 函    数：I2C起始
 * 参    数：无
 * 返 回 值：无
 */
void MyI2C_Start(void)
{
	MyI2C_W_SDA(1); // 释放SDA，确保SDA为高电平
	MyI2C_W_SCL(1); // 释放SCL，确保SCL为高电平
	MyI2C_W_SDA(0); // 在SCL高电平期间，拉低SDA，产生起始信号
	MyI2C_W_SCL(0); // 起始后把SCL也拉低，即为了占用总线，也为了方便总线时序的拼接
}

/**
 * 函    数：I2C终止
 * 参    数：无
 * 返 回 值：无
 */
void MyI2C_Stop(void)
{
	MyI2C_W_SDA(0); // 拉低SDA，确保SDA为低电平
	MyI2C_W_SCL(1); // 释放SCL，使SCL呈现高电平
	MyI2C_W_SDA(1); // 在SCL高电平期间，释放SDA，产生终止信号
}

/**
 * 函    数：I2C发送一个字节
 * 参    数：Byte 要发送的一个字节数据，范围：0x00~0xFF
 * 返 回 值：无
 */
void MyI2C_SendByte(uint8_t Byte)
{
	uint8_t i;
	for (i = 0; i < 8; i++) // 循环8次，主机依次发送数据的每一位
	{
		/*两个!可以对数据进行两次逻辑取反，作用是把非0值统一转换为1，即：!!(0) = 0，!!(非0) = 1*/
		MyI2C_W_SDA(!!(Byte & (0x80 >> i))); // 使用掩码的方式取出Byte的指定一位数据并写入到SDA线
		MyI2C_W_SCL(1);						 // 释放SCL，从机在SCL高电平期间读取SDA
		MyI2C_W_SCL(0);						 // 拉低SCL，主机开始发送下一位数据
	}
}

/**
 * 函    数：I2C接收一个字节
 * 参    数：无
 * 返 回 值：接收到的一个字节数据，范围：0x00~0xFF
 */
uint8_t MyI2C_ReceiveByte(void)
{
	uint8_t i, Byte = 0x00; // 定义接收的数据，并赋初值0x00，此处必须赋初值0x00，后面会用到
	MyI2C_W_SDA(1);			// 接收前，主机先确保释放SDA，避免干扰从机的数据发送
	for (i = 0; i < 8; i++) // 循环8次，主机依次接收数据的每一位
	{
		MyI2C_W_SCL(1); // 释放SCL，主机机在SCL高电平期间读取SDA
		if (MyI2C_R_SDA())
		{
			Byte |= (0x80 >> i);
		} // 读取SDA数据，并存储到Byte变量
		  // 当SDA为1时，置变量指定位为1，当SDA为0时，不做处理，指定位为默认的初值0
		MyI2C_W_SCL(0); // 拉低SCL，从机在SCL低电平期间写入SDA
	}
	return Byte; // 返回接收到的一个字节数据
}

/**
 * 函    数：I2C发送应答位
 * 参    数：Byte 要发送的应答位，范围：0~1，0表示应答，1表示非应答
 * 返 回 值：无
 */
void MyI2C_SendAck(uint8_t AckBit)
{
	MyI2C_W_SDA(AckBit); // 主机把应答位数据放到SDA线
	MyI2C_W_SCL(1);		 // 释放SCL，从机在SCL高电平期间，读取应答位
	MyI2C_W_SCL(0);		 // 拉低SCL，开始下一个时序模块
}

/**
 * 函    数：I2C接收应答位
 * 参    数：无
 * 返 回 值：接收到的应答位，范围：0~1，0表示应答，1表示非应答
 */
uint8_t MyI2C_ReceiveAck(void)
{
	uint8_t AckBit;			// 定义应答位变量
	MyI2C_W_SDA(1);			// 接收前，主机先确保释放SDA，避免干扰从机的数据发送
	MyI2C_W_SCL(1);			// 释放SCL，主机机在SCL高电平期间读取SDA
	AckBit = MyI2C_R_SDA(); // 将应答位存储到变量里
	MyI2C_W_SCL(0);			// 拉低SCL，开始下一个时序模块
	return AckBit;			// 返回定义应答位变量
}
