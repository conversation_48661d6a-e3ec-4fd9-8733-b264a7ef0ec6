[{"directory": "c:\\Users\\<USER>\\Downloads\\newTI\\TI_2025\\EIDE", "file": "c:\\Users\\<USER>\\Downloads\\newTI\\TI_2025\\Control\\DataScope_DP.C", "command": "\"C:\\Users\\<USER>\\AppData\\Local\\Keil_v5\\ARM\\ARMCLANG\\bin\\armclang.exe\" --target=arm-arm-none-eabi -c -xc -std=c99 -I../source -I../source/third_party/CMSIS/Core/Include -I../Hardware -I.. -I../Control -I.cmsis/include -I../keil/RTE/_MSPM0G3507_Project -D\"__MSPM0G3507__\" -D\"MPU6050\" -D\"MOTION_DRIVER_TARGET_MSPM0\" -mcpu=cortex-m0plus -mlittle-endian -D__MICROLIB -O0 -ffunction-sections -funsigned-char -fshort-enums -fshort-wchar -fno-rtti -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -g -o ./build/MSPM0G3507_Project/.obj/__/Control/DataScope_DP.o -MMD ./../Control/DataScope_DP.C"}, {"directory": "c:\\Users\\<USER>\\Downloads\\newTI\\TI_2025\\EIDE", "file": "c:\\Users\\<USER>\\Downloads\\newTI\\TI_2025\\Control\\control.c", "command": "\"C:\\Users\\<USER>\\AppData\\Local\\Keil_v5\\ARM\\ARMCLANG\\bin\\armclang.exe\" --target=arm-arm-none-eabi -c -xc -std=c99 -I../source -I../source/third_party/CMSIS/Core/Include -I../Hardware -I.. -I../Control -I.cmsis/include -I../keil/RTE/_MSPM0G3507_Project -D\"__MSPM0G3507__\" -D\"MPU6050\" -D\"MOTION_DRIVER_TARGET_MSPM0\" -mcpu=cortex-m0plus -mlittle-endian -D__MICROLIB -O0 -ffunction-sections -funsigned-char -fshort-enums -fshort-wchar -fno-rtti -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -g -o ./build/MSPM0G3507_Project/.obj/__/Control/control.o -MMD ./../Control/control.c"}, {"directory": "c:\\Users\\<USER>\\Downloads\\newTI\\TI_2025\\EIDE", "file": "c:\\Users\\<USER>\\Downloads\\newTI\\TI_2025\\Control\\show.c", "command": "\"C:\\Users\\<USER>\\AppData\\Local\\Keil_v5\\ARM\\ARMCLANG\\bin\\armclang.exe\" --target=arm-arm-none-eabi -c -xc -std=c99 -I../source -I../source/third_party/CMSIS/Core/Include -I../Hardware -I.. -I../Control -I.cmsis/include -I../keil/RTE/_MSPM0G3507_Project -D\"__MSPM0G3507__\" -D\"MPU6050\" -D\"MOTION_DRIVER_TARGET_MSPM0\" -mcpu=cortex-m0plus -mlittle-endian -D__MICROLIB -O0 -ffunction-sections -funsigned-char -fshort-enums -fshort-wchar -fno-rtti -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -g -o ./build/MSPM0G3507_Project/.obj/__/Control/show.o -MMD ./../Control/show.c"}, {"directory": "c:\\Users\\<USER>\\Downloads\\newTI\\TI_2025\\EIDE", "file": "c:\\Users\\<USER>\\Downloads\\newTI\\TI_2025\\Control\\uart_callback.c", "command": "\"C:\\Users\\<USER>\\AppData\\Local\\Keil_v5\\ARM\\ARMCLANG\\bin\\armclang.exe\" --target=arm-arm-none-eabi -c -xc -std=c99 -I../source -I../source/third_party/CMSIS/Core/Include -I../Hardware -I.. -I../Control -I.cmsis/include -I../keil/RTE/_MSPM0G3507_Project -D\"__MSPM0G3507__\" -D\"MPU6050\" -D\"MOTION_DRIVER_TARGET_MSPM0\" -mcpu=cortex-m0plus -mlittle-endian -D__MICROLIB -O0 -ffunction-sections -funsigned-char -fshort-enums -fshort-wchar -fno-rtti -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -g -o ./build/MSPM0G3507_Project/.obj/__/Control/uart_callback.o -MMD ./../Control/uart_callback.c"}, {"directory": "c:\\Users\\<USER>\\Downloads\\newTI\\TI_2025\\EIDE", "file": "c:\\Users\\<USER>\\Downloads\\newTI\\TI_2025\\Hardware\\IR_Module.c", "command": "\"C:\\Users\\<USER>\\AppData\\Local\\Keil_v5\\ARM\\ARMCLANG\\bin\\armclang.exe\" --target=arm-arm-none-eabi -c -xc -std=c99 -I../source -I../source/third_party/CMSIS/Core/Include -I../Hardware -I.. -I../Control -I.cmsis/include -I../keil/RTE/_MSPM0G3507_Project -D\"__MSPM0G3507__\" -D\"MPU6050\" -D\"MOTION_DRIVER_TARGET_MSPM0\" -mcpu=cortex-m0plus -mlittle-endian -D__MICROLIB -O0 -ffunction-sections -funsigned-char -fshort-enums -fshort-wchar -fno-rtti -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -g -o ./build/MSPM0G3507_Project/.obj/__/Hardware/IR_Module.o -MMD ./../Hardware/IR_Module.c"}, {"directory": "c:\\Users\\<USER>\\Downloads\\newTI\\TI_2025\\EIDE", "file": "c:\\Users\\<USER>\\Downloads\\newTI\\TI_2025\\Hardware\\adc.c", "command": "\"C:\\Users\\<USER>\\AppData\\Local\\Keil_v5\\ARM\\ARMCLANG\\bin\\armclang.exe\" --target=arm-arm-none-eabi -c -xc -std=c99 -I../source -I../source/third_party/CMSIS/Core/Include -I../Hardware -I.. -I../Control -I.cmsis/include -I../keil/RTE/_MSPM0G3507_Project -D\"__MSPM0G3507__\" -D\"MPU6050\" -D\"MOTION_DRIVER_TARGET_MSPM0\" -mcpu=cortex-m0plus -mlittle-endian -D__MICROLIB -O0 -ffunction-sections -funsigned-char -fshort-enums -fshort-wchar -fno-rtti -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -g -o ./build/MSPM0G3507_Project/.obj/__/Hardware/adc.o -MMD ./../Hardware/adc.c"}, {"directory": "c:\\Users\\<USER>\\Downloads\\newTI\\TI_2025\\EIDE", "file": "c:\\Users\\<USER>\\Downloads\\newTI\\TI_2025\\Hardware\\board.c", "command": "\"C:\\Users\\<USER>\\AppData\\Local\\Keil_v5\\ARM\\ARMCLANG\\bin\\armclang.exe\" --target=arm-arm-none-eabi -c -xc -std=c99 -I../source -I../source/third_party/CMSIS/Core/Include -I../Hardware -I.. -I../Control -I.cmsis/include -I../keil/RTE/_MSPM0G3507_Project -D\"__MSPM0G3507__\" -D\"MPU6050\" -D\"MOTION_DRIVER_TARGET_MSPM0\" -mcpu=cortex-m0plus -mlittle-endian -D__MICROLIB -O0 -ffunction-sections -funsigned-char -fshort-enums -fshort-wchar -fno-rtti -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -g -o ./build/MSPM0G3507_Project/.obj/__/Hardware/board.o -MMD ./../Hardware/board.c"}, {"directory": "c:\\Users\\<USER>\\Downloads\\newTI\\TI_2025\\EIDE", "file": "c:\\Users\\<USER>\\Downloads\\newTI\\TI_2025\\Hardware\\encoder.c", "command": "\"C:\\Users\\<USER>\\AppData\\Local\\Keil_v5\\ARM\\ARMCLANG\\bin\\armclang.exe\" --target=arm-arm-none-eabi -c -xc -std=c99 -I../source -I../source/third_party/CMSIS/Core/Include -I../Hardware -I.. -I../Control -I.cmsis/include -I../keil/RTE/_MSPM0G3507_Project -D\"__MSPM0G3507__\" -D\"MPU6050\" -D\"MOTION_DRIVER_TARGET_MSPM0\" -mcpu=cortex-m0plus -mlittle-endian -D__MICROLIB -O0 -ffunction-sections -funsigned-char -fshort-enums -fshort-wchar -fno-rtti -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -g -o ./build/MSPM0G3507_Project/.obj/__/Hardware/encoder.o -MMD ./../Hardware/encoder.c"}, {"directory": "c:\\Users\\<USER>\\Downloads\\newTI\\TI_2025\\EIDE", "file": "c:\\Users\\<USER>\\Downloads\\newTI\\TI_2025\\Hardware\\key.c", "command": "\"C:\\Users\\<USER>\\AppData\\Local\\Keil_v5\\ARM\\ARMCLANG\\bin\\armclang.exe\" --target=arm-arm-none-eabi -c -xc -std=c99 -I../source -I../source/third_party/CMSIS/Core/Include -I../Hardware -I.. -I../Control -I.cmsis/include -I../keil/RTE/_MSPM0G3507_Project -D\"__MSPM0G3507__\" -D\"MPU6050\" -D\"MOTION_DRIVER_TARGET_MSPM0\" -mcpu=cortex-m0plus -mlittle-endian -D__MICROLIB -O0 -ffunction-sections -funsigned-char -fshort-enums -fshort-wchar -fno-rtti -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -g -o ./build/MSPM0G3507_Project/.obj/__/Hardware/key.o -MMD ./../Hardware/key.c"}, {"directory": "c:\\Users\\<USER>\\Downloads\\newTI\\TI_2025\\EIDE", "file": "c:\\Users\\<USER>\\Downloads\\newTI\\TI_2025\\Hardware\\led.c", "command": "\"C:\\Users\\<USER>\\AppData\\Local\\Keil_v5\\ARM\\ARMCLANG\\bin\\armclang.exe\" --target=arm-arm-none-eabi -c -xc -std=c99 -I../source -I../source/third_party/CMSIS/Core/Include -I../Hardware -I.. -I../Control -I.cmsis/include -I../keil/RTE/_MSPM0G3507_Project -D\"__MSPM0G3507__\" -D\"MPU6050\" -D\"MOTION_DRIVER_TARGET_MSPM0\" -mcpu=cortex-m0plus -mlittle-endian -D__MICROLIB -O0 -ffunction-sections -funsigned-char -fshort-enums -fshort-wchar -fno-rtti -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -g -o ./build/MSPM0G3507_Project/.obj/__/Hardware/led.o -MMD ./../Hardware/led.c"}, {"directory": "c:\\Users\\<USER>\\Downloads\\newTI\\TI_2025\\EIDE", "file": "c:\\Users\\<USER>\\Downloads\\newTI\\TI_2025\\Hardware\\motor.c", "command": "\"C:\\Users\\<USER>\\AppData\\Local\\Keil_v5\\ARM\\ARMCLANG\\bin\\armclang.exe\" --target=arm-arm-none-eabi -c -xc -std=c99 -I../source -I../source/third_party/CMSIS/Core/Include -I../Hardware -I.. -I../Control -I.cmsis/include -I../keil/RTE/_MSPM0G3507_Project -D\"__MSPM0G3507__\" -D\"MPU6050\" -D\"MOTION_DRIVER_TARGET_MSPM0\" -mcpu=cortex-m0plus -mlittle-endian -D__MICROLIB -O0 -ffunction-sections -funsigned-char -fshort-enums -fshort-wchar -fno-rtti -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -g -o ./build/MSPM0G3507_Project/.obj/__/Hardware/motor.o -MMD ./../Hardware/motor.c"}, {"directory": "c:\\Users\\<USER>\\Downloads\\newTI\\TI_2025\\EIDE", "file": "c:\\Users\\<USER>\\Downloads\\newTI\\TI_2025\\Hardware\\oled.c", "command": "\"C:\\Users\\<USER>\\AppData\\Local\\Keil_v5\\ARM\\ARMCLANG\\bin\\armclang.exe\" --target=arm-arm-none-eabi -c -xc -std=c99 -I../source -I../source/third_party/CMSIS/Core/Include -I../Hardware -I.. -I../Control -I.cmsis/include -I../keil/RTE/_MSPM0G3507_Project -D\"__MSPM0G3507__\" -D\"MPU6050\" -D\"MOTION_DRIVER_TARGET_MSPM0\" -mcpu=cortex-m0plus -mlittle-endian -D__MICROLIB -O0 -ffunction-sections -funsigned-char -fshort-enums -fshort-wchar -fno-rtti -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -g -o ./build/MSPM0G3507_Project/.obj/__/Hardware/oled.o -MMD ./../Hardware/oled.c"}, {"directory": "c:\\Users\\<USER>\\Downloads\\newTI\\TI_2025\\EIDE", "file": "c:\\Users\\<USER>\\Downloads\\newTI\\TI_2025\\code\\MPU6050.c", "command": "\"C:\\Users\\<USER>\\AppData\\Local\\Keil_v5\\ARM\\ARMCLANG\\bin\\armclang.exe\" --target=arm-arm-none-eabi -c -xc -std=c99 -I../source -I../source/third_party/CMSIS/Core/Include -I../Hardware -I.. -I../Control -I.cmsis/include -I../keil/RTE/_MSPM0G3507_Project -D\"__MSPM0G3507__\" -D\"MPU6050\" -D\"MOTION_DRIVER_TARGET_MSPM0\" -mcpu=cortex-m0plus -mlittle-endian -D__MICROLIB -O0 -ffunction-sections -funsigned-char -fshort-enums -fshort-wchar -fno-rtti -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -g -o ./build/MSPM0G3507_Project/.obj/__/code/MPU6050.o -MMD ./../code/MPU6050.c"}, {"directory": "c:\\Users\\<USER>\\Downloads\\newTI\\TI_2025\\EIDE", "file": "c:\\Users\\<USER>\\Downloads\\newTI\\TI_2025\\code\\MyI2C.c", "command": "\"C:\\Users\\<USER>\\AppData\\Local\\Keil_v5\\ARM\\ARMCLANG\\bin\\armclang.exe\" --target=arm-arm-none-eabi -c -xc -std=c99 -I../source -I../source/third_party/CMSIS/Core/Include -I../Hardware -I.. -I../Control -I.cmsis/include -I../keil/RTE/_MSPM0G3507_Project -D\"__MSPM0G3507__\" -D\"MPU6050\" -D\"MOTION_DRIVER_TARGET_MSPM0\" -mcpu=cortex-m0plus -mlittle-endian -D__MICROLIB -O0 -ffunction-sections -funsigned-char -fshort-enums -fshort-wchar -fno-rtti -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -g -o ./build/MSPM0G3507_Project/.obj/__/code/MyI2C.o -MMD ./../code/MyI2C.c"}, {"directory": "c:\\Users\\<USER>\\Downloads\\newTI\\TI_2025\\EIDE", "file": "c:\\Users\\<USER>\\Downloads\\newTI\\TI_2025\\empty.c", "command": "\"C:\\Users\\<USER>\\AppData\\Local\\Keil_v5\\ARM\\ARMCLANG\\bin\\armclang.exe\" --target=arm-arm-none-eabi -c -xc -std=c99 -I../source -I../source/third_party/CMSIS/Core/Include -I../Hardware -I.. -I../Control -I.cmsis/include -I../keil/RTE/_MSPM0G3507_Project -D\"__MSPM0G3507__\" -D\"MPU6050\" -D\"MOTION_DRIVER_TARGET_MSPM0\" -mcpu=cortex-m0plus -mlittle-endian -D__MICROLIB -O0 -ffunction-sections -funsigned-char -fshort-enums -fshort-wchar -fno-rtti -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -g -o ./build/MSPM0G3507_Project/.obj/__/empty.o -MMD ./../empty.c"}, {"directory": "c:\\Users\\<USER>\\Downloads\\newTI\\TI_2025\\EIDE", "file": "c:\\Users\\<USER>\\Downloads\\newTI\\TI_2025\\keil\\startup_mspm0g350x_uvision.s", "command": "\"C:\\Users\\<USER>\\AppData\\Local\\Keil_v5\\ARM\\ARMCLANG\\bin\\armasm.exe\" --diag_suppress=A1950 -I../source -I../source/third_party/CMSIS/Core/Include -I../Hardware -I.. -I../Control -I.cmsis/include -I../keil/RTE/_MSPM0G3507_Project --cpu Cortex-M0+ --li --pd \"__MICROLIB SETA 1\" -g -o ./build/MSPM0G3507_Project/.obj/__/keil/startup_mspm0g350x_uvision.o --depend ./build/MSPM0G3507_Project/.obj/__/keil/startup_mspm0g350x_uvision.d ./../keil/startup_mspm0g350x_uvision.s"}, {"directory": "c:\\Users\\<USER>\\Downloads\\newTI\\TI_2025\\EIDE", "file": "c:\\Users\\<USER>\\Downloads\\newTI\\TI_2025\\source\\ti\\driverlib\\m0p\\dl_interrupt.c", "command": "\"C:\\Users\\<USER>\\AppData\\Local\\Keil_v5\\ARM\\ARMCLANG\\bin\\armclang.exe\" --target=arm-arm-none-eabi -c -xc -std=c99 -I../source -I../source/third_party/CMSIS/Core/Include -I../Hardware -I.. -I../Control -I.cmsis/include -I../keil/RTE/_MSPM0G3507_Project -D\"__MSPM0G3507__\" -D\"MPU6050\" -D\"MOTION_DRIVER_TARGET_MSPM0\" -mcpu=cortex-m0plus -mlittle-endian -D__MICROLIB -O0 -ffunction-sections -funsigned-char -fshort-enums -fshort-wchar -fno-rtti -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -g -o ./build/MSPM0G3507_Project/.obj/__/source/ti/driverlib/m0p/dl_interrupt.o -MMD ./../source/ti/driverlib/m0p/dl_interrupt.c"}, {"directory": "c:\\Users\\<USER>\\Downloads\\newTI\\TI_2025\\EIDE", "file": "c:\\Users\\<USER>\\Downloads\\newTI\\TI_2025\\ti\\dl_adc12.c", "command": "\"C:\\Users\\<USER>\\AppData\\Local\\Keil_v5\\ARM\\ARMCLANG\\bin\\armclang.exe\" --target=arm-arm-none-eabi -c -xc -std=c99 -I../source -I../source/third_party/CMSIS/Core/Include -I../Hardware -I.. -I../Control -I.cmsis/include -I../keil/RTE/_MSPM0G3507_Project -D\"__MSPM0G3507__\" -D\"MPU6050\" -D\"MOTION_DRIVER_TARGET_MSPM0\" -mcpu=cortex-m0plus -mlittle-endian -D__MICROLIB -O0 -ffunction-sections -funsigned-char -fshort-enums -fshort-wchar -fno-rtti -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -g -o ./build/MSPM0G3507_Project/.obj/__/ti/dl_adc12.o -MMD ./../ti/dl_adc12.c"}, {"directory": "c:\\Users\\<USER>\\Downloads\\newTI\\TI_2025\\EIDE", "file": "c:\\Users\\<USER>\\Downloads\\newTI\\TI_2025\\ti\\dl_aes.c", "command": "\"C:\\Users\\<USER>\\AppData\\Local\\Keil_v5\\ARM\\ARMCLANG\\bin\\armclang.exe\" --target=arm-arm-none-eabi -c -xc -std=c99 -I../source -I../source/third_party/CMSIS/Core/Include -I../Hardware -I.. -I../Control -I.cmsis/include -I../keil/RTE/_MSPM0G3507_Project -D\"__MSPM0G3507__\" -D\"MPU6050\" -D\"MOTION_DRIVER_TARGET_MSPM0\" -mcpu=cortex-m0plus -mlittle-endian -D__MICROLIB -O0 -ffunction-sections -funsigned-char -fshort-enums -fshort-wchar -fno-rtti -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -g -o ./build/MSPM0G3507_Project/.obj/__/ti/dl_aes.o -MMD ./../ti/dl_aes.c"}, {"directory": "c:\\Users\\<USER>\\Downloads\\newTI\\TI_2025\\EIDE", "file": "c:\\Users\\<USER>\\Downloads\\newTI\\TI_2025\\ti\\dl_aesadv.c", "command": "\"C:\\Users\\<USER>\\AppData\\Local\\Keil_v5\\ARM\\ARMCLANG\\bin\\armclang.exe\" --target=arm-arm-none-eabi -c -xc -std=c99 -I../source -I../source/third_party/CMSIS/Core/Include -I../Hardware -I.. -I../Control -I.cmsis/include -I../keil/RTE/_MSPM0G3507_Project -D\"__MSPM0G3507__\" -D\"MPU6050\" -D\"MOTION_DRIVER_TARGET_MSPM0\" -mcpu=cortex-m0plus -mlittle-endian -D__MICROLIB -O0 -ffunction-sections -funsigned-char -fshort-enums -fshort-wchar -fno-rtti -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -g -o ./build/MSPM0G3507_Project/.obj/__/ti/dl_aesadv.o -MMD ./../ti/dl_aesadv.c"}, {"directory": "c:\\Users\\<USER>\\Downloads\\newTI\\TI_2025\\EIDE", "file": "c:\\Users\\<USER>\\Downloads\\newTI\\TI_2025\\ti\\dl_common.c", "command": "\"C:\\Users\\<USER>\\AppData\\Local\\Keil_v5\\ARM\\ARMCLANG\\bin\\armclang.exe\" --target=arm-arm-none-eabi -c -xc -std=c99 -I../source -I../source/third_party/CMSIS/Core/Include -I../Hardware -I.. -I../Control -I.cmsis/include -I../keil/RTE/_MSPM0G3507_Project -D\"__MSPM0G3507__\" -D\"MPU6050\" -D\"MOTION_DRIVER_TARGET_MSPM0\" -mcpu=cortex-m0plus -mlittle-endian -D__MICROLIB -O0 -ffunction-sections -funsigned-char -fshort-enums -fshort-wchar -fno-rtti -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -g -o ./build/MSPM0G3507_Project/.obj/__/ti/dl_common.o -MMD ./../ti/dl_common.c"}, {"directory": "c:\\Users\\<USER>\\Downloads\\newTI\\TI_2025\\EIDE", "file": "c:\\Users\\<USER>\\Downloads\\newTI\\TI_2025\\ti\\dl_crc.c", "command": "\"C:\\Users\\<USER>\\AppData\\Local\\Keil_v5\\ARM\\ARMCLANG\\bin\\armclang.exe\" --target=arm-arm-none-eabi -c -xc -std=c99 -I../source -I../source/third_party/CMSIS/Core/Include -I../Hardware -I.. -I../Control -I.cmsis/include -I../keil/RTE/_MSPM0G3507_Project -D\"__MSPM0G3507__\" -D\"MPU6050\" -D\"MOTION_DRIVER_TARGET_MSPM0\" -mcpu=cortex-m0plus -mlittle-endian -D__MICROLIB -O0 -ffunction-sections -funsigned-char -fshort-enums -fshort-wchar -fno-rtti -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -g -o ./build/MSPM0G3507_Project/.obj/__/ti/dl_crc.o -MMD ./../ti/dl_crc.c"}, {"directory": "c:\\Users\\<USER>\\Downloads\\newTI\\TI_2025\\EIDE", "file": "c:\\Users\\<USER>\\Downloads\\newTI\\TI_2025\\ti\\dl_crcp.c", "command": "\"C:\\Users\\<USER>\\AppData\\Local\\Keil_v5\\ARM\\ARMCLANG\\bin\\armclang.exe\" --target=arm-arm-none-eabi -c -xc -std=c99 -I../source -I../source/third_party/CMSIS/Core/Include -I../Hardware -I.. -I../Control -I.cmsis/include -I../keil/RTE/_MSPM0G3507_Project -D\"__MSPM0G3507__\" -D\"MPU6050\" -D\"MOTION_DRIVER_TARGET_MSPM0\" -mcpu=cortex-m0plus -mlittle-endian -D__MICROLIB -O0 -ffunction-sections -funsigned-char -fshort-enums -fshort-wchar -fno-rtti -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -g -o ./build/MSPM0G3507_Project/.obj/__/ti/dl_crcp.o -MMD ./../ti/dl_crcp.c"}, {"directory": "c:\\Users\\<USER>\\Downloads\\newTI\\TI_2025\\EIDE", "file": "c:\\Users\\<USER>\\Downloads\\newTI\\TI_2025\\ti\\dl_dac12.c", "command": "\"C:\\Users\\<USER>\\AppData\\Local\\Keil_v5\\ARM\\ARMCLANG\\bin\\armclang.exe\" --target=arm-arm-none-eabi -c -xc -std=c99 -I../source -I../source/third_party/CMSIS/Core/Include -I../Hardware -I.. -I../Control -I.cmsis/include -I../keil/RTE/_MSPM0G3507_Project -D\"__MSPM0G3507__\" -D\"MPU6050\" -D\"MOTION_DRIVER_TARGET_MSPM0\" -mcpu=cortex-m0plus -mlittle-endian -D__MICROLIB -O0 -ffunction-sections -funsigned-char -fshort-enums -fshort-wchar -fno-rtti -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -g -o ./build/MSPM0G3507_Project/.obj/__/ti/dl_dac12.o -MMD ./../ti/dl_dac12.c"}, {"directory": "c:\\Users\\<USER>\\Downloads\\newTI\\TI_2025\\EIDE", "file": "c:\\Users\\<USER>\\Downloads\\newTI\\TI_2025\\ti\\dl_dma.c", "command": "\"C:\\Users\\<USER>\\AppData\\Local\\Keil_v5\\ARM\\ARMCLANG\\bin\\armclang.exe\" --target=arm-arm-none-eabi -c -xc -std=c99 -I../source -I../source/third_party/CMSIS/Core/Include -I../Hardware -I.. -I../Control -I.cmsis/include -I../keil/RTE/_MSPM0G3507_Project -D\"__MSPM0G3507__\" -D\"MPU6050\" -D\"MOTION_DRIVER_TARGET_MSPM0\" -mcpu=cortex-m0plus -mlittle-endian -D__MICROLIB -O0 -ffunction-sections -funsigned-char -fshort-enums -fshort-wchar -fno-rtti -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -g -o ./build/MSPM0G3507_Project/.obj/__/ti/dl_dma.o -MMD ./../ti/dl_dma.c"}, {"directory": "c:\\Users\\<USER>\\Downloads\\newTI\\TI_2025\\EIDE", "file": "c:\\Users\\<USER>\\Downloads\\newTI\\TI_2025\\ti\\dl_flashctl.c", "command": "\"C:\\Users\\<USER>\\AppData\\Local\\Keil_v5\\ARM\\ARMCLANG\\bin\\armclang.exe\" --target=arm-arm-none-eabi -c -xc -std=c99 -I../source -I../source/third_party/CMSIS/Core/Include -I../Hardware -I.. -I../Control -I.cmsis/include -I../keil/RTE/_MSPM0G3507_Project -D\"__MSPM0G3507__\" -D\"MPU6050\" -D\"MOTION_DRIVER_TARGET_MSPM0\" -mcpu=cortex-m0plus -mlittle-endian -D__MICROLIB -O0 -ffunction-sections -funsigned-char -fshort-enums -fshort-wchar -fno-rtti -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -g -o ./build/MSPM0G3507_Project/.obj/__/ti/dl_flashctl.o -MMD ./../ti/dl_flashctl.c"}, {"directory": "c:\\Users\\<USER>\\Downloads\\newTI\\TI_2025\\EIDE", "file": "c:\\Users\\<USER>\\Downloads\\newTI\\TI_2025\\ti\\dl_i2c.c", "command": "\"C:\\Users\\<USER>\\AppData\\Local\\Keil_v5\\ARM\\ARMCLANG\\bin\\armclang.exe\" --target=arm-arm-none-eabi -c -xc -std=c99 -I../source -I../source/third_party/CMSIS/Core/Include -I../Hardware -I.. -I../Control -I.cmsis/include -I../keil/RTE/_MSPM0G3507_Project -D\"__MSPM0G3507__\" -D\"MPU6050\" -D\"MOTION_DRIVER_TARGET_MSPM0\" -mcpu=cortex-m0plus -mlittle-endian -D__MICROLIB -O0 -ffunction-sections -funsigned-char -fshort-enums -fshort-wchar -fno-rtti -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -g -o ./build/MSPM0G3507_Project/.obj/__/ti/dl_i2c.o -MMD ./../ti/dl_i2c.c"}, {"directory": "c:\\Users\\<USER>\\Downloads\\newTI\\TI_2025\\EIDE", "file": "c:\\Users\\<USER>\\Downloads\\newTI\\TI_2025\\ti\\dl_keystorectl.c", "command": "\"C:\\Users\\<USER>\\AppData\\Local\\Keil_v5\\ARM\\ARMCLANG\\bin\\armclang.exe\" --target=arm-arm-none-eabi -c -xc -std=c99 -I../source -I../source/third_party/CMSIS/Core/Include -I../Hardware -I.. -I../Control -I.cmsis/include -I../keil/RTE/_MSPM0G3507_Project -D\"__MSPM0G3507__\" -D\"MPU6050\" -D\"MOTION_DRIVER_TARGET_MSPM0\" -mcpu=cortex-m0plus -mlittle-endian -D__MICROLIB -O0 -ffunction-sections -funsigned-char -fshort-enums -fshort-wchar -fno-rtti -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -g -o ./build/MSPM0G3507_Project/.obj/__/ti/dl_keystorectl.o -MMD ./../ti/dl_keystorectl.c"}, {"directory": "c:\\Users\\<USER>\\Downloads\\newTI\\TI_2025\\EIDE", "file": "c:\\Users\\<USER>\\Downloads\\newTI\\TI_2025\\ti\\dl_lcd.c", "command": "\"C:\\Users\\<USER>\\AppData\\Local\\Keil_v5\\ARM\\ARMCLANG\\bin\\armclang.exe\" --target=arm-arm-none-eabi -c -xc -std=c99 -I../source -I../source/third_party/CMSIS/Core/Include -I../Hardware -I.. -I../Control -I.cmsis/include -I../keil/RTE/_MSPM0G3507_Project -D\"__MSPM0G3507__\" -D\"MPU6050\" -D\"MOTION_DRIVER_TARGET_MSPM0\" -mcpu=cortex-m0plus -mlittle-endian -D__MICROLIB -O0 -ffunction-sections -funsigned-char -fshort-enums -fshort-wchar -fno-rtti -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -g -o ./build/MSPM0G3507_Project/.obj/__/ti/dl_lcd.o -MMD ./../ti/dl_lcd.c"}, {"directory": "c:\\Users\\<USER>\\Downloads\\newTI\\TI_2025\\EIDE", "file": "c:\\Users\\<USER>\\Downloads\\newTI\\TI_2025\\ti\\dl_lfss.c", "command": "\"C:\\Users\\<USER>\\AppData\\Local\\Keil_v5\\ARM\\ARMCLANG\\bin\\armclang.exe\" --target=arm-arm-none-eabi -c -xc -std=c99 -I../source -I../source/third_party/CMSIS/Core/Include -I../Hardware -I.. -I../Control -I.cmsis/include -I../keil/RTE/_MSPM0G3507_Project -D\"__MSPM0G3507__\" -D\"MPU6050\" -D\"MOTION_DRIVER_TARGET_MSPM0\" -mcpu=cortex-m0plus -mlittle-endian -D__MICROLIB -O0 -ffunction-sections -funsigned-char -fshort-enums -fshort-wchar -fno-rtti -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -g -o ./build/MSPM0G3507_Project/.obj/__/ti/dl_lfss.o -MMD ./../ti/dl_lfss.c"}, {"directory": "c:\\Users\\<USER>\\Downloads\\newTI\\TI_2025\\EIDE", "file": "c:\\Users\\<USER>\\Downloads\\newTI\\TI_2025\\ti\\dl_mathacl.c", "command": "\"C:\\Users\\<USER>\\AppData\\Local\\Keil_v5\\ARM\\ARMCLANG\\bin\\armclang.exe\" --target=arm-arm-none-eabi -c -xc -std=c99 -I../source -I../source/third_party/CMSIS/Core/Include -I../Hardware -I.. -I../Control -I.cmsis/include -I../keil/RTE/_MSPM0G3507_Project -D\"__MSPM0G3507__\" -D\"MPU6050\" -D\"MOTION_DRIVER_TARGET_MSPM0\" -mcpu=cortex-m0plus -mlittle-endian -D__MICROLIB -O0 -ffunction-sections -funsigned-char -fshort-enums -fshort-wchar -fno-rtti -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -g -o ./build/MSPM0G3507_Project/.obj/__/ti/dl_mathacl.o -MMD ./../ti/dl_mathacl.c"}, {"directory": "c:\\Users\\<USER>\\Downloads\\newTI\\TI_2025\\EIDE", "file": "c:\\Users\\<USER>\\Downloads\\newTI\\TI_2025\\ti\\dl_mcan.c", "command": "\"C:\\Users\\<USER>\\AppData\\Local\\Keil_v5\\ARM\\ARMCLANG\\bin\\armclang.exe\" --target=arm-arm-none-eabi -c -xc -std=c99 -I../source -I../source/third_party/CMSIS/Core/Include -I../Hardware -I.. -I../Control -I.cmsis/include -I../keil/RTE/_MSPM0G3507_Project -D\"__MSPM0G3507__\" -D\"MPU6050\" -D\"MOTION_DRIVER_TARGET_MSPM0\" -mcpu=cortex-m0plus -mlittle-endian -D__MICROLIB -O0 -ffunction-sections -funsigned-char -fshort-enums -fshort-wchar -fno-rtti -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -g -o ./build/MSPM0G3507_Project/.obj/__/ti/dl_mcan.o -MMD ./../ti/dl_mcan.c"}, {"directory": "c:\\Users\\<USER>\\Downloads\\newTI\\TI_2025\\EIDE", "file": "c:\\Users\\<USER>\\Downloads\\newTI\\TI_2025\\ti\\dl_opa.c", "command": "\"C:\\Users\\<USER>\\AppData\\Local\\Keil_v5\\ARM\\ARMCLANG\\bin\\armclang.exe\" --target=arm-arm-none-eabi -c -xc -std=c99 -I../source -I../source/third_party/CMSIS/Core/Include -I../Hardware -I.. -I../Control -I.cmsis/include -I../keil/RTE/_MSPM0G3507_Project -D\"__MSPM0G3507__\" -D\"MPU6050\" -D\"MOTION_DRIVER_TARGET_MSPM0\" -mcpu=cortex-m0plus -mlittle-endian -D__MICROLIB -O0 -ffunction-sections -funsigned-char -fshort-enums -fshort-wchar -fno-rtti -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -g -o ./build/MSPM0G3507_Project/.obj/__/ti/dl_opa.o -MMD ./../ti/dl_opa.c"}, {"directory": "c:\\Users\\<USER>\\Downloads\\newTI\\TI_2025\\EIDE", "file": "c:\\Users\\<USER>\\Downloads\\newTI\\TI_2025\\ti\\dl_rtc_common.c", "command": "\"C:\\Users\\<USER>\\AppData\\Local\\Keil_v5\\ARM\\ARMCLANG\\bin\\armclang.exe\" --target=arm-arm-none-eabi -c -xc -std=c99 -I../source -I../source/third_party/CMSIS/Core/Include -I../Hardware -I.. -I../Control -I.cmsis/include -I../keil/RTE/_MSPM0G3507_Project -D\"__MSPM0G3507__\" -D\"MPU6050\" -D\"MOTION_DRIVER_TARGET_MSPM0\" -mcpu=cortex-m0plus -mlittle-endian -D__MICROLIB -O0 -ffunction-sections -funsigned-char -fshort-enums -fshort-wchar -fno-rtti -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -g -o ./build/MSPM0G3507_Project/.obj/__/ti/dl_rtc_common.o -MMD ./../ti/dl_rtc_common.c"}, {"directory": "c:\\Users\\<USER>\\Downloads\\newTI\\TI_2025\\EIDE", "file": "c:\\Users\\<USER>\\Downloads\\newTI\\TI_2025\\ti\\dl_spi.c", "command": "\"C:\\Users\\<USER>\\AppData\\Local\\Keil_v5\\ARM\\ARMCLANG\\bin\\armclang.exe\" --target=arm-arm-none-eabi -c -xc -std=c99 -I../source -I../source/third_party/CMSIS/Core/Include -I../Hardware -I.. -I../Control -I.cmsis/include -I../keil/RTE/_MSPM0G3507_Project -D\"__MSPM0G3507__\" -D\"MPU6050\" -D\"MOTION_DRIVER_TARGET_MSPM0\" -mcpu=cortex-m0plus -mlittle-endian -D__MICROLIB -O0 -ffunction-sections -funsigned-char -fshort-enums -fshort-wchar -fno-rtti -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -g -o ./build/MSPM0G3507_Project/.obj/__/ti/dl_spi.o -MMD ./../ti/dl_spi.c"}, {"directory": "c:\\Users\\<USER>\\Downloads\\newTI\\TI_2025\\EIDE", "file": "c:\\Users\\<USER>\\Downloads\\newTI\\TI_2025\\ti\\dl_timer.c", "command": "\"C:\\Users\\<USER>\\AppData\\Local\\Keil_v5\\ARM\\ARMCLANG\\bin\\armclang.exe\" --target=arm-arm-none-eabi -c -xc -std=c99 -I../source -I../source/third_party/CMSIS/Core/Include -I../Hardware -I.. -I../Control -I.cmsis/include -I../keil/RTE/_MSPM0G3507_Project -D\"__MSPM0G3507__\" -D\"MPU6050\" -D\"MOTION_DRIVER_TARGET_MSPM0\" -mcpu=cortex-m0plus -mlittle-endian -D__MICROLIB -O0 -ffunction-sections -funsigned-char -fshort-enums -fshort-wchar -fno-rtti -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -g -o ./build/MSPM0G3507_Project/.obj/__/ti/dl_timer.o -MMD ./../ti/dl_timer.c"}, {"directory": "c:\\Users\\<USER>\\Downloads\\newTI\\TI_2025\\EIDE", "file": "c:\\Users\\<USER>\\Downloads\\newTI\\TI_2025\\ti\\dl_trng.c", "command": "\"C:\\Users\\<USER>\\AppData\\Local\\Keil_v5\\ARM\\ARMCLANG\\bin\\armclang.exe\" --target=arm-arm-none-eabi -c -xc -std=c99 -I../source -I../source/third_party/CMSIS/Core/Include -I../Hardware -I.. -I../Control -I.cmsis/include -I../keil/RTE/_MSPM0G3507_Project -D\"__MSPM0G3507__\" -D\"MPU6050\" -D\"MOTION_DRIVER_TARGET_MSPM0\" -mcpu=cortex-m0plus -mlittle-endian -D__MICROLIB -O0 -ffunction-sections -funsigned-char -fshort-enums -fshort-wchar -fno-rtti -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -g -o ./build/MSPM0G3507_Project/.obj/__/ti/dl_trng.o -MMD ./../ti/dl_trng.c"}, {"directory": "c:\\Users\\<USER>\\Downloads\\newTI\\TI_2025\\EIDE", "file": "c:\\Users\\<USER>\\Downloads\\newTI\\TI_2025\\ti\\dl_uart.c", "command": "\"C:\\Users\\<USER>\\AppData\\Local\\Keil_v5\\ARM\\ARMCLANG\\bin\\armclang.exe\" --target=arm-arm-none-eabi -c -xc -std=c99 -I../source -I../source/third_party/CMSIS/Core/Include -I../Hardware -I.. -I../Control -I.cmsis/include -I../keil/RTE/_MSPM0G3507_Project -D\"__MSPM0G3507__\" -D\"MPU6050\" -D\"MOTION_DRIVER_TARGET_MSPM0\" -mcpu=cortex-m0plus -mlittle-endian -D__MICROLIB -O0 -ffunction-sections -funsigned-char -fshort-enums -fshort-wchar -fno-rtti -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -g -o ./build/MSPM0G3507_Project/.obj/__/ti/dl_uart.o -MMD ./../ti/dl_uart.c"}, {"directory": "c:\\Users\\<USER>\\Downloads\\newTI\\TI_2025\\EIDE", "file": "c:\\Users\\<USER>\\Downloads\\newTI\\TI_2025\\ti\\dl_vref.c", "command": "\"C:\\Users\\<USER>\\AppData\\Local\\Keil_v5\\ARM\\ARMCLANG\\bin\\armclang.exe\" --target=arm-arm-none-eabi -c -xc -std=c99 -I../source -I../source/third_party/CMSIS/Core/Include -I../Hardware -I.. -I../Control -I.cmsis/include -I../keil/RTE/_MSPM0G3507_Project -D\"__MSPM0G3507__\" -D\"MPU6050\" -D\"MOTION_DRIVER_TARGET_MSPM0\" -mcpu=cortex-m0plus -mlittle-endian -D__MICROLIB -O0 -ffunction-sections -funsigned-char -fshort-enums -fshort-wchar -fno-rtti -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -g -o ./build/MSPM0G3507_Project/.obj/__/ti/dl_vref.o -MMD ./../ti/dl_vref.c"}, {"directory": "c:\\Users\\<USER>\\Downloads\\newTI\\TI_2025\\EIDE", "file": "c:\\Users\\<USER>\\Downloads\\newTI\\TI_2025\\ti_msp_dl_config.c", "command": "\"C:\\Users\\<USER>\\AppData\\Local\\Keil_v5\\ARM\\ARMCLANG\\bin\\armclang.exe\" --target=arm-arm-none-eabi -c -xc -std=c99 -I../source -I../source/third_party/CMSIS/Core/Include -I../Hardware -I.. -I../Control -I.cmsis/include -I../keil/RTE/_MSPM0G3507_Project -D\"__MSPM0G3507__\" -D\"MPU6050\" -D\"MOTION_DRIVER_TARGET_MSPM0\" -mcpu=cortex-m0plus -mlittle-endian -D__MICROLIB -O0 -ffunction-sections -funsigned-char -fshort-enums -fshort-wchar -fno-rtti -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -g -o ./build/MSPM0G3507_Project/.obj/__/ti_msp_dl_config.o -MMD ./../ti_msp_dl_config.c"}]