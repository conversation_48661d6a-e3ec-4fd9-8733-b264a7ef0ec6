#include "MPU6050_Reg.h"
#include "MPU6050.h"
#include "MyI2C.h"
#include "oled.h"
#define ACC_THRESHOLD 1.0f   // 加速度阈值，单位为 g
#define GYRO_THRESHOLD 40.0f // 角速度阈值，单位为 °/s
#define DISPLAY_TIMEOUT 5000 // 显示屏点亮时间，单位为毫秒
// mpu6050误差校准变量
int16_t AccX_offset = 0, AccY_offset = 0, AccZ_offset = 0;
int16_t GyroX_offset = 0, GyroY_offset = 0, GyroZ_offset = 0;
// 旋转角
float Z_Angle = 0.0f;        // 累计的 Z 轴旋转角度
uint32_t lastUpdateTime = 0; // 上一次更新的时间
uint32_t g_msTicks = 0;      // 系统毫秒计数器
/* clang-format off */
// 按需修改下面代码

void Delay_Us(uint32_t ustime)
{
	while(ustime--)
	{
		__NOP();__NOP();__NOP();__NOP();__NOP();__NOP();__NOP();__NOP();__NOP();__NOP();__NOP();__NOP();
		__NOP();__NOP();__NOP();__NOP();__NOP();__NOP();__NOP();__NOP();__NOP();__NOP();__NOP();__NOP();
		__NOP();__NOP();__NOP();__NOP();__NOP();__NOP();__NOP();__NOP();__NOP();__NOP();__NOP();__NOP();
		__NOP();__NOP();__NOP();__NOP();__NOP();__NOP();__NOP();__NOP();__NOP();__NOP();__NOP();__NOP();
		__NOP();__NOP();__NOP();__NOP();__NOP();__NOP();__NOP();__NOP();__NOP();__NOP();__NOP();__NOP();
		__NOP();__NOP();__NOP();__NOP();__NOP();__NOP();__NOP();__NOP();__NOP();__NOP();__NOP();__NOP();
		__NOP();__NOP();__NOP();__NOP();__NOP();__NOP();__NOP();__NOP();__NOP();__NOP();__NOP();__NOP();
		__NOP();__NOP();__NOP();__NOP();__NOP();__NOP();__NOP();__NOP();__NOP();__NOP();__NOP();__NOP();
		__NOP();__NOP();__NOP();__NOP();
	}
}

/* clang-format on */

void MPU6050_WriteReg(uint8_t RegAddress, uint8_t Data)
{
    MyI2C_Start();                   // I2C起始
    MyI2C_SendByte(MPU6050_ADDRESS); // 发送从机地址，读写位为0，表示即将写入
    MyI2C_ReceiveAck();              // 接收应答
    MyI2C_SendByte(RegAddress);      // 发送寄存器地址
    MyI2C_ReceiveAck();              // 接收应答
    MyI2C_SendByte(Data);            // 发送要写入寄存器的数据
    MyI2C_ReceiveAck();              // 接收应答
    MyI2C_Stop();                    // I2C终止
    Delay_Us(1);
}

/**
 * 函    数：MPU6050读寄存器
 * 参    数：RegAddress 寄存器地址，范围：参考MPU6050手册的寄存器描述
 * 返 回 值：读取寄存器的数据，范围：0x00~0xFF
 */
uint8_t MPU6050_ReadReg(uint8_t RegAddress)
{
    uint8_t Data;

    MyI2C_Start();                   // I2C起始
    MyI2C_SendByte(MPU6050_ADDRESS); // 发送从机地址，读写位为0，表示即将写入
    MyI2C_ReceiveAck();              // 接收应答
    MyI2C_SendByte(RegAddress);      // 发送寄存器地址
    MyI2C_ReceiveAck();              // 接收应答

    MyI2C_Start();                          // I2C重复起始
    MyI2C_SendByte(MPU6050_ADDRESS | 0x01); // 发送从机地址，读写位为1，表示即将读取
    MyI2C_ReceiveAck();                     // 接收应答
    Data = MyI2C_ReceiveByte();             // 接收指定寄存器的数据
    MyI2C_SendAck(1);                       // 发送应答，给从机非应答，终止从机的数据输出
    MyI2C_Stop();                           // I2C终止

    return Data;
}
/* clang-format on */
// mpu6050校准
void MPU6050_Calibrate(void)
{
    int32_t AccX_sum = 0, AccY_sum = 0, AccZ_sum = 0;
    int32_t GyroX_sum = 0, GyroY_sum = 0, GyroZ_sum = 0;
    int16_t AccX, AccY, AccZ, GyroX, GyroY, GyroZ;

    // 采样 500 次，计算平均值
    for (int i = 0; i < 500; i++)
    {
        MPU6050_GetData(&AccX, &AccY, &AccZ, &GyroX, &GyroY, &GyroZ);
        AccX_sum += AccX;
        AccY_sum += AccY;
        AccZ_sum += AccZ;
        GyroX_sum += GyroX;
        GyroY_sum += GyroY;
        GyroZ_sum += GyroZ;
    }

    // 计算偏移量
    AccX_offset = AccX_sum / 500;
    AccY_offset = AccY_sum / 500;
    AccZ_offset = (AccZ_sum / 500) - 16384; // 静止状态下 Z 轴加速度计应为 1g
    GyroX_offset = GyroX_sum / 500;
    GyroY_offset = GyroY_sum / 500;
    GyroZ_offset = GyroZ_sum / 500;
}
// 初始化
void MPU6050_Init(void)
{
    /*MPU6050寄存器初始化，需要对照MPU6050手册的寄存器描述配置，此处仅配置了部分重要的寄存器*/
    MPU6050_WriteReg(MPU6050_PWR_MGMT_1, 0x01);   // 电源管理寄存器1，取消睡眠模式，选择时钟源为X轴陀螺仪
    MPU6050_WriteReg(MPU6050_PWR_MGMT_2, 0x00);   // 电源管理寄存器2，保持默认值0，所有轴均不待机
    MPU6050_WriteReg(MPU6050_SMPLRT_DIV, 0x09);   // 采样率分频寄存器，配置采样率
    MPU6050_WriteReg(MPU6050_CONFIG, 0x06);       // 配置寄存器，配置DLPF
    MPU6050_WriteReg(MPU6050_GYRO_CONFIG, 0x00);  // 陀螺仪配置寄存器，选择满量程为±250°/s。
    MPU6050_WriteReg(MPU6050_ACCEL_CONFIG, 0x00); // 加速度计配置寄存器，选择满量程为±2g
}
uint8_t MPU6050_GetID(void)
{
    return MPU6050_ReadReg(MPU6050_WHO_AM_I);
}
// 获取数据
void MPU6050_GetData(int16_t *AccX, int16_t *AccY, int16_t *AccZ,
                     int16_t *GyroX, int16_t *GyroY, int16_t *GyroZ)
{
    uint8_t DataH, DataL; // 定义数据高8位和低8位的变量

    DataH = MPU6050_ReadReg(MPU6050_ACCEL_XOUT_H); // 读取加速度计X轴的高8位数据
    DataL = MPU6050_ReadReg(MPU6050_ACCEL_XOUT_L); // 读取加速度计X轴的低8位数据
    *AccX = (DataH << 8) | DataL;                  // 数据拼接，通过输出参数返回

    DataH = MPU6050_ReadReg(MPU6050_ACCEL_YOUT_H); // 读取加速度计Y轴的高8位数据
    DataL = MPU6050_ReadReg(MPU6050_ACCEL_YOUT_L); // 读取加速度计Y轴的低8位数据
    *AccY = (DataH << 8) | DataL;                  // 数据拼接，通过输出参数返回

    DataH = MPU6050_ReadReg(MPU6050_ACCEL_ZOUT_H); // 读取加速度计Z轴的高8位数据
    DataL = MPU6050_ReadReg(MPU6050_ACCEL_ZOUT_L); // 读取加速度计Z轴的低8位数据
    *AccZ = (DataH << 8) | DataL;                  // 数据拼接，通过输出参数返回

    DataH = MPU6050_ReadReg(MPU6050_GYRO_XOUT_H); // 读取陀螺仪X轴的高8位数据
    DataL = MPU6050_ReadReg(MPU6050_GYRO_XOUT_L); // 读取陀螺仪X轴的低8位数据
    *GyroX = (DataH << 8) | DataL;                // 数据拼接，通过输出参数返回

    DataH = MPU6050_ReadReg(MPU6050_GYRO_YOUT_H); // 读取陀螺仪Y轴的高8位数据
    DataL = MPU6050_ReadReg(MPU6050_GYRO_YOUT_L); // 读取陀螺仪Y轴的低8位数据
    *GyroY = (DataH << 8) | DataL;                // 数据拼接，通过输出参数返回

    DataH = MPU6050_ReadReg(MPU6050_GYRO_ZOUT_H); // 读取陀螺仪Z轴的高8位数据
    DataL = MPU6050_ReadReg(MPU6050_GYRO_ZOUT_L); // 读取陀螺仪Z轴的低8位数据
    *GyroZ = (DataH << 8) | DataL;                // 数据拼接，通过输出参数返回
}
// 获取计算后数据
void MPU6050_GetRealData(float *AccX, float *AccY, float *AccZ,
                         float *GyroX, float *GyroY, float *GyroZ)
{
    int16_t rawAccX, rawAccY, rawAccZ;
    int16_t rawGyroX, rawGyroY, rawGyroZ;

    uint8_t DataH, DataL;

    DataH = MPU6050_ReadReg(MPU6050_ACCEL_XOUT_H);
    DataL = MPU6050_ReadReg(MPU6050_ACCEL_XOUT_L);
    rawAccX = (DataH << 8) | DataL;

    DataH = MPU6050_ReadReg(MPU6050_ACCEL_YOUT_H);
    DataL = MPU6050_ReadReg(MPU6050_ACCEL_YOUT_L);
    rawAccY = (DataH << 8) | DataL;

    DataH = MPU6050_ReadReg(MPU6050_ACCEL_ZOUT_H);
    DataL = MPU6050_ReadReg(MPU6050_ACCEL_ZOUT_L);
    rawAccZ = (DataH << 8) | DataL;

    DataH = MPU6050_ReadReg(MPU6050_GYRO_XOUT_H);
    DataL = MPU6050_ReadReg(MPU6050_GYRO_XOUT_L);
    rawGyroX = (DataH << 8) | DataL;

    DataH = MPU6050_ReadReg(MPU6050_GYRO_YOUT_H);
    DataL = MPU6050_ReadReg(MPU6050_GYRO_YOUT_L);
    rawGyroY = (DataH << 8) | DataL;

    DataH = MPU6050_ReadReg(MPU6050_GYRO_ZOUT_H);
    DataL = MPU6050_ReadReg(MPU6050_GYRO_ZOUT_L);
    rawGyroZ = (DataH << 8) | DataL;

    // 将原始数据转换为实际物理值
    *AccX = rawAccX / 16384.0f;
    *AccY = rawAccY / 16384.0f;
    *AccZ = rawAccZ / 16384.0f;

    *GyroX = rawGyroX / 131.0f;
    *GyroY = rawGyroY / 131.0f;
    *GyroZ = rawGyroZ / 131.0f;
}
// 获取校准后原始数据
void MPU6050_GetCalibratedData(int16_t *AccX, int16_t *AccY, int16_t *AccZ,
                               int16_t *GyroX, int16_t *GyroY, int16_t *GyroZ)
{
    int16_t rawAccX, rawAccY, rawAccZ;
    int16_t rawGyroX, rawGyroY, rawGyroZ;

    // 获取原始数据
    MPU6050_GetData(&rawAccX, &rawAccY, &rawAccZ, &rawGyroX, &rawGyroY, &rawGyroZ);

    // 减去偏移量
    *AccX = rawAccX - AccX_offset;
    *AccY = rawAccY - AccY_offset;
    *AccZ = rawAccZ - AccZ_offset;
    *GyroX = rawGyroX - GyroX_offset;
    *GyroY = rawGyroY - GyroY_offset;
    *GyroZ = rawGyroZ - GyroZ_offset;
}
void SysTick_Handler(void)
{
    g_msTicks++; // 每1ms中断一次
}
uint32_t TI_GetTick(void)
{
    return g_msTicks;
}
// 计算旋转角
void MPU6050_CalculateZAngle(float gyroZ)
{
    uint32_t currentTime = TI_GetTick();                // 获取当前时间（毫秒）
    float dt = (currentTime - lastUpdateTime) / 1000.0f; // 计算时间间隔（秒）

    if (lastUpdateTime != 0)
    {
        // 更新累计角度
        Z_Angle += gyroZ * dt / 131.0f;
    }
    // 更新上一次更新时间
    lastUpdateTime = currentTime;
}

void MPU6050_ZiTaiJieSuan(void)
{
    int16_t accx, accy, accz, gyrox, gyroy, gyroz;
    MPU6050_GetCalibratedData(&accx, &accy, &accz, &gyrox, &gyroy, &gyroz);
    MPU6050_CalculateZAngle(gyroz);
}
