>>> cc

./../Control/show.c:34:52: warning: passing 'char [5]' to parameter of type 'const uint8_t *' (aka 'const unsigned char *') converts between pointers to integer types with different sign [-Wpointer-sign]
             if(Car_Mode==0)   OLED_ShowString(0,0,"Mec ");
                                                   ^~~~~~
../Hardware/oled.h:22:57: note: passing argument to parameter 'p' here
void OLED_ShowString(uint8_t x,uint8_t y,const uint8_t *p);
                                                        ^
./../Control/show.c:35:52: warning: passing 'char [5]' to parameter of type 'const uint8_t *' (aka 'const unsigned char *') converts between pointers to integer types with different sign [-Wpointer-sign]
        else if(Car_Mode==1)   OLED_ShowString(0,0,"Omni");
                                                   ^~~~~~
../Hardware/oled.h:22:57: note: passing argument to parameter 'p' here
void OLED_ShowString(uint8_t x,uint8_t y,const uint8_t *p);
                                                        ^
./../Control/show.c:36:52: warning: passing 'char [5]' to parameter of type 'const uint8_t *' (aka 'const unsigned char *') converts between pointers to integer types with different sign [-Wpointer-sign]
        else if(Car_Mode==2)   OLED_ShowString(0,0,"AKM ");
                                                   ^~~~~~
../Hardware/oled.h:22:57: note: passing argument to parameter 'p' here
void OLED_ShowString(uint8_t x,uint8_t y,const uint8_t *p);
                                                        ^
./../Control/show.c:37:52: warning: passing 'char [5]' to parameter of type 'const uint8_t *' (aka 'const unsigned char *') converts between pointers to integer types with different sign [-Wpointer-sign]
        else if(Car_Mode==3)   OLED_ShowString(0,0,"Diff");
                                                   ^~~~~~
../Hardware/oled.h:22:57: note: passing argument to parameter 'p' here
void OLED_ShowString(uint8_t x,uint8_t y,const uint8_t *p);
                                                        ^
./../Control/show.c:38:52: warning: passing 'char [5]' to parameter of type 'const uint8_t *' (aka 'const unsigned char *') converts between pointers to integer types with different sign [-Wpointer-sign]
        else if(Car_Mode==4)   OLED_ShowString(0,0,"4WD ");
                                                   ^~~~~~
../Hardware/oled.h:22:57: note: passing argument to parameter 'p' here
void OLED_ShowString(uint8_t x,uint8_t y,const uint8_t *p);
                                                        ^
./../Control/show.c:39:46: warning: passing 'char [5]' to parameter of type 'const uint8_t *' (aka 'const unsigned char *') converts between pointers to integer types with different sign [-Wpointer-sign]
                else if(Car_Mode==5)   OLED_ShowString(0,0,"Tank");
                                                           ^~~~~~
../Hardware/oled.h:22:57: note: passing argument to parameter 'p' here
void OLED_ShowString(uint8_t x,uint8_t y,const uint8_t *p);
                                                        ^
./../Control/show.c:41:45: warning: passing 'char [4]' to parameter of type 'const uint8_t *' (aka 'const unsigned char *') converts between pointers to integer types with different sign [-Wpointer-sign]
            if(Run_Mode==0)   OLED_ShowString(90,0,"APP");
                                                   ^~~~~
../Hardware/oled.h:22:57: note: passing argument to parameter 'p' here
void OLED_ShowString(uint8_t x,uint8_t y,const uint8_t *p);
                                                        ^
./../Control/show.c:42:47: warning: passing 'char [4]' to parameter of type 'const uint8_t *' (aka 'const unsigned char *') converts between pointers to integer types with different sign [-Wpointer-sign]
                else if(Run_Mode==1)   OLED_ShowString(90,0,"IRF");
                                                            ^~~~~
../Hardware/oled.h:22:57: note: passing argument to parameter 'p' here
void OLED_ShowString(uint8_t x,uint8_t y,const uint8_t *p);
                                                        ^
./../Control/show.c:47:25: warning: passing 'char [3]' to parameter of type 'const uint8_t *' (aka 'const unsigned char *') converts between pointers to integer types with different sign [-Wpointer-sign]
                OLED_ShowString(00,20,"VZ");
                                      ^~~~
../Hardware/oled.h:22:57: note: passing argument to parameter 'p' here
void OLED_ShowString(uint8_t x,uint8_t y,const uint8_t *p);
                                                        ^
./../Control/show.c:48:42: warning: passing 'char [2]' to parameter of type 'const uint8_t *' (aka 'const unsigned char *') converts between pointers to integer types with different sign [-Wpointer-sign]
                if( Move_Z<0)    OLED_ShowString(48,20,"-");
                                                       ^~~
../Hardware/oled.h:22:57: note: passing argument to parameter 'p' here
void OLED_ShowString(uint8_t x,uint8_t y,const uint8_t *p);
                                                        ^
./../Control/show.c:49:42: warning: passing 'char [2]' to parameter of type 'const uint8_t *' (aka 'const unsigned char *') converts between pointers to integer types with different sign [-Wpointer-sign]
                if(Move_Z>=0)    OLED_ShowString(48,20,"+");
                                                       ^~~
../Hardware/oled.h:22:57: note: passing argument to parameter 'p' here
void OLED_ShowString(uint8_t x,uint8_t y,const uint8_t *p);
                                                        ^
./../Control/show.c:52:53: warning: passing 'char [2]' to parameter of type 'const uint8_t *' (aka 'const unsigned char *') converts between pointers to integer types with different sign [-Wpointer-sign]
                              OLED_ShowString(00,30,"L");
                                                    ^~~
../Hardware/oled.h:22:57: note: passing argument to parameter 'p' here
void OLED_ShowString(uint8_t x,uint8_t y,const uint8_t *p);
                                                        ^
./../Control/show.c:53:75: warning: passing 'char [2]' to parameter of type 'const uint8_t *' (aka 'const unsigned char *') converts between pointers to integer types with different sign [-Wpointer-sign]
        if((MotorA.Target_Encoder*1000)<0)          OLED_ShowString(16,30,"-"),
                                                                          ^~~
../Hardware/oled.h:22:57: note: passing argument to parameter 'p' here
void OLED_ShowString(uint8_t x,uint8_t y,const uint8_t *p);
                                                        ^
./../Control/show.c:55:73: warning: passing 'char [2]' to parameter of type 'const uint8_t *' (aka 'const unsigned char *') converts between pointers to integer types with different sign [-Wpointer-sign]
        if((MotorA.Target_Encoder*1000)>=0)       OLED_ShowString(16,30,"+"),
                                                                        ^~~
../Hardware/oled.h:22:57: note: passing argument to parameter 'p' here
void OLED_ShowString(uint8_t x,uint8_t y,const uint8_t *p);
                                                        ^
./../Control/show.c:58:62: warning: passing 'char [2]' to parameter of type 'const uint8_t *' (aka 'const unsigned char *') converts between pointers to integer types with different sign [-Wpointer-sign]
        if(MotorA.Current_Encoder<0)   OLED_ShowString(60,30,"-");
                                                             ^~~
../Hardware/oled.h:22:57: note: passing argument to parameter 'p' here
void OLED_ShowString(uint8_t x,uint8_t y,const uint8_t *p);
                                                        ^
./../Control/show.c:59:64: warning: passing 'char [2]' to parameter of type 'const uint8_t *' (aka 'const unsigned char *') converts between pointers to integer types with different sign [-Wpointer-sign]
        if(MotorA.Current_Encoder>=0)    OLED_ShowString(60,30,"+");
                                                               ^~~
../Hardware/oled.h:22:57: note: passing argument to parameter 'p' here
void OLED_ShowString(uint8_t x,uint8_t y,const uint8_t *p);
                                                        ^
./../Control/show.c:61:75: warning: passing 'char [5]' to parameter of type 'const uint8_t *' (aka 'const unsigned char *') converts between pointers to integer types with different sign [-Wpointer-sign]
                                                    OLED_ShowString(96,30,"mm/s");
                                                                          ^~~~~~
../Hardware/oled.h:22:57: note: passing argument to parameter 'p' here
void OLED_ShowString(uint8_t x,uint8_t y,const uint8_t *p);
                                                        ^
./../Control/show.c:64:53: warning: passing 'char [2]' to parameter of type 'const uint8_t *' (aka 'const unsigned char *') converts between pointers to integer types with different sign [-Wpointer-sign]
                              OLED_ShowString(00,40,"R");
                                                    ^~~
../Hardware/oled.h:22:57: note: passing argument to parameter 'p' here
void OLED_ShowString(uint8_t x,uint8_t y,const uint8_t *p);
                                                        ^
./../Control/show.c:65:74: warning: passing 'char [2]' to parameter of type 'const uint8_t *' (aka 'const unsigned char *') converts between pointers to integer types with different sign [-Wpointer-sign]
        if((MotorB.Target_Encoder*1000)<0)         OLED_ShowString(16,40,"-"),
                                                                         ^~~
../Hardware/oled.h:22:57: note: passing argument to parameter 'p' here
void OLED_ShowString(uint8_t x,uint8_t y,const uint8_t *p);
                                                        ^
./../Control/show.c:67:72: warning: passing 'char [2]' to parameter of type 'const uint8_t *' (aka 'const unsigned char *') converts between pointers to integer types with different sign [-Wpointer-sign]
        if((MotorB.Target_Encoder*1000)>=0)             OLED_ShowString(16,40,"+"),
                                                                              ^~~
../Hardware/oled.h:22:57: note: passing argument to parameter 'p' here
void OLED_ShowString(uint8_t x,uint8_t y,const uint8_t *p);
                                                        ^
./../Control/show.c:70:63: warning: passing 'char [2]' to parameter of type 'const uint8_t *' (aka 'const unsigned char *') converts between pointers to integer types with different sign [-Wpointer-sign]
        if(MotorB.Current_Encoder<0)    OLED_ShowString(60,40,"-");
                                                              ^~~
../Hardware/oled.h:22:57: note: passing argument to parameter 'p' here
void OLED_ShowString(uint8_t x,uint8_t y,const uint8_t *p);
                                                        ^
./../Control/show.c:71:63: warning: passing 'char [2]' to parameter of type 'const uint8_t *' (aka 'const unsigned char *') converts between pointers to integer types with different sign [-Wpointer-sign]
        if(MotorB.Current_Encoder>=0)   OLED_ShowString(60,40,"+");
                                                              ^~~
../Hardware/oled.h:22:57: note: passing argument to parameter 'p' here
void OLED_ShowString(uint8_t x,uint8_t y,const uint8_t *p);
                                                        ^
./../Control/show.c:73:75: warning: passing 'char [5]' to parameter of type 'const uint8_t *' (aka 'const unsigned char *') converts between pointers to integer types with different sign [-Wpointer-sign]
                                                    OLED_ShowString(96,40,"mm/s");
                                                                          ^~~~~~
../Hardware/oled.h:22:57: note: passing argument to parameter 'p' here
void OLED_ShowString(uint8_t x,uint8_t y,const uint8_t *p);
                                                        ^
./../Control/show.c:76:52: warning: passing 'char [2]' to parameter of type 'const uint8_t *' (aka 'const unsigned char *') converts between pointers to integer types with different sign [-Wpointer-sign]
                              OLED_ShowString(0,50,"V");
                                                   ^~~
../Hardware/oled.h:22:57: note: passing argument to parameter 'p' here
void OLED_ShowString(uint8_t x,uint8_t y,const uint8_t *p);
                                                        ^
./../Control/show.c:77:75: warning: passing 'char [2]' to parameter of type 'const uint8_t *' (aka 'const unsigned char *') converts between pointers to integer types with different sign [-Wpointer-sign]
                                                    OLED_ShowString(30,50,".");
                                                                          ^~~
../Hardware/oled.h:22:57: note: passing argument to parameter 'p' here
void OLED_ShowString(uint8_t x,uint8_t y,const uint8_t *p);
                                                        ^
./../Control/show.c:78:75: warning: passing 'char [2]' to parameter of type 'const uint8_t *' (aka 'const unsigned char *') converts between pointers to integer types with different sign [-Wpointer-sign]
                                                    OLED_ShowString(64,50,"V");
                                                                          ^~~
../Hardware/oled.h:22:57: note: passing argument to parameter 'p' here
void OLED_ShowString(uint8_t x,uint8_t y,const uint8_t *p);
                                                        ^
./../Control/show.c:81:53: warning: passing 'char [4]' to parameter of type 'const uint8_t *' (aka 'const unsigned char *') converts between pointers to integer types with different sign [-Wpointer-sign]
        if(Flag_Stop)         OLED_ShowString(95,50,"OFF");
                                                    ^~~~~
../Hardware/oled.h:22:57: note: passing argument to parameter 'p' here
void OLED_ShowString(uint8_t x,uint8_t y,const uint8_t *p);
                                                        ^
./../Control/show.c:82:53: warning: passing 'char [4]' to parameter of type 'const uint8_t *' (aka 'const unsigned char *') converts between pointers to integer types with different sign [-Wpointer-sign]
        if(!Flag_Stop)        OLED_ShowString(95,50,"ON ");
                                                    ^~~~~
../Hardware/oled.h:22:57: note: passing argument to parameter 'p' here
void OLED_ShowString(uint8_t x,uint8_t y,const uint8_t *p);
                                                        ^
28 warnings generated.

>>> ld

"C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCLANG\bin\..\lib\armlib\mc_p.l:SELECTION_SCRIPT", line 2982 (column 9): Error: L6907E: Expected an expression.
Not enough information to list image symbols.
Not enough information to list the image map.
Finished: 2 information, 0 warning and 1 error messages.
