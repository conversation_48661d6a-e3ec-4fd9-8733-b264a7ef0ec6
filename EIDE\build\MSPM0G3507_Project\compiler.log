>>> cc

./../ti/dl_flashctl.c:60:2: error: "Compiler not supported for this function"
#error "Compiler not supported for this function"
 ^
./../ti/dl_flashctl.c:62:1: error: unknown type name 'RAMFUNC'
RAMFUNC static DL_FLASHCTL_COMMAND_STATUS DL_FlashCTL_executeCommandFromRAM(
^
./../ti/dl_flashctl.c:62:9: error: expected identifier or '('
RAMFUNC static DL_FLASHCTL_COMMAND_STATUS DL_FlashCTL_executeCommandFromRAM(
        ^
3 errors generated.

>>> ld

