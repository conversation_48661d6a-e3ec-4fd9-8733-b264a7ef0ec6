--cpu Cortex-M0+
".\objects\startup_mspm0g350x_uvision.o"
".\objects\ti_msp_dl_config.o"
".\objects\empty.o"
".\objects\board.o"
".\objects\dl_vref.o"
".\objects\dl_uart.o"
".\objects\dl_trng.o"
".\objects\dl_timer.o"
".\objects\dl_spi.o"
".\objects\dl_rtc_common.o"
".\objects\dl_opa.o"
".\objects\dl_mcan.o"
".\objects\dl_mathacl.o"
".\objects\dl_lfss.o"
".\objects\dl_lcd.o"
".\objects\dl_keystorectl.o"
".\objects\dl_i2c.o"
".\objects\dl_flashctl.o"
".\objects\dl_dma.o"
".\objects\dl_dac12.o"
".\objects\dl_crcp.o"
".\objects\dl_crc.o"
".\objects\dl_common.o"
".\objects\dl_aesadv.o"
".\objects\dl_aes.o"
".\objects\dl_adc12.o"
".\objects\dl_interrupt.o"
".\objects\oled.o"
".\objects\key.o"
".\objects\led.o"
".\objects\motor.o"
".\objects\encoder.o"
".\objects\adc.o"
".\objects\control.o"
".\objects\show.o"
".\objects\datascope_dp.o"
".\objects\uart_callback.o"
".\objects\mpu6050.o"
".\objects\myi2c.o"
--library_type=microlib --strict --scatter ".\mspm0g3507.sct"
../source/ti/driverlib/lib/keil/m0p/mspm0g1x0x_g3x0x/driverlib.a --summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list ".\Listings\empty_LP_MSPM0G3507_nortos_keil.map" -o .\Objects\empty_LP_MSPM0G3507_nortos_keil.axf