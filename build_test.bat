@echo off
echo Testing ARM Clang compilation...

set ARMCLANG_PATH="C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCLANG\bin\armclang.exe"
set ARMLINK_PATH="C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCLANG\bin\armlink.exe"

echo Compiling a simple test file...
%ARMCLANG_PATH% --target=arm-arm-none-eabi -c -xc -std=c99 -Isource -Isource/third_party/CMSIS/Core/Include -IHardware -I. -IControl -I.cmsis/include -Ikeil/RTE/_MSPM0G3507_Project -D"__MSPM0G3507__" -D"MPU6050" -D"MOTION_DRIVER_TARGET_MSPM0" -mcpu=cortex-m0plus -mlittle-endian -O0 -ffunction-sections -funsigned-char -fshort-enums -fshort-wchar -fno-rtti -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -g -o test_dl_flashctl.o ti/dl_flashctl.c

if %ERRORLEVEL% EQU 0 (
    echo Compilation successful!
    del test_dl_flashctl.o
) else (
    echo Compilation failed with error code %ERRORLEVEL%
)

pause
